import { createRouter } from '@tanstack/react-router'
import { routeTree } from './routeTree.gen'
import * as Sentry from '@sentry/react'

export const router = createRouter({
    routeTree,
    defaultOnCatch(error, errorInfo) {
        Sentry.withScope((scope) => {
            scope.setTag('scope', 'router')
            scope.setContext('routerErrorInfo', {
                errorInfo: JSON.stringify(errorInfo),
            })
            Sentry.captureException(error)
        })
    },
})

declare module '@tanstack/react-router' {
    interface Register {
        router: typeof router
    }
}
