import type { <PERSON>a, StoryObj } from '@storybook/react-vite'

import { But<PERSON> } from './button'

const meta = {
    component: Button,
} satisfies Meta<typeof Button>

export default meta

type Story = StoryObj<typeof meta>

export const Default: Story = {
    args: {
        children: 'Press me',
        variant: 'default',
        size: 'default',
    },
}

export const Destructive: Story = {
    args: {
        children: 'Press me',
        variant: 'destructive',
        size: 'default',
    },
}
