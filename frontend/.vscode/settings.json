{
    // TanStack Router best practices
    "files.readonlyInclude": {
        "**/routeTree.gen.ts": true
    },
    "files.watcherExclude": {
        "**/routeTree.gen.ts": true
    },
    "search.exclude": {
        "**/routeTree.gen.ts": true
    },

    // Pre<PERSON>er & <PERSON> on save
    "editor.formatOnSave": true,
    "editor.formatOnSaveMode": "file",
    "[typescriptreact]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[typescript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[javascript]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[json]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "[jsonc]": {
        "editor.defaultFormatter": "esbenp.prettier-vscode"
    },
    "editor.codeActionsOnSave": {
        "source.fixAll.eslint": "explicit"
    },
    "typescript.tsdk": "node_modules/typescript/lib"
}
