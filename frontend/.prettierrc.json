{"$schema": "https://json.schemastore.org/prettierrc", "semi": false, "useTabs": false, "singleQuote": true, "jsxSingleQuote": false, "bracketSpacing": true, "bracketSameLine": false, "singleAttributePerLine": true, "arrowParens": "always", "trailingComma": "all", "quoteProps": "as-needed", "endOfLine": "lf", "overrides": [{"files": ".*rc", "options": {"parser": "json"}}], "plugins": ["prettier-plugin-multiline-arrays"], "multilineArraysWrapThreshold": 2}